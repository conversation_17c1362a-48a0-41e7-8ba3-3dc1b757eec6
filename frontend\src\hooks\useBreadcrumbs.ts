import { useLocation } from 'react-router-dom';
import { useMemo } from 'react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

// Define route mappings for better breadcrumb labels
const routeLabels: Record<string, string> = {
  dashboard: 'Dashboard',
  transactions: 'Transactions',
  add: 'Add Transaction',
  edit: 'Edit Transaction',
  accounts: 'Accounts & Wallets',
  bank: 'Bank Accounts',
  cash: 'Cash & Wallets',
  credit: 'Credit Cards',
  budgets: 'Budgets',
  create: 'Create Budget',
  tracking: 'Budget Tracking',
  goals: 'Goals',
  savings: 'Savings Goals',
  debt: 'Debt Repayment',
  reports: 'Reports',
  expenses: 'Expense Reports',
  'income-expenses': 'Income vs Expenses',
  monthly: 'Monthly Summary',
  yearly: 'Yearly Summary',
  analytics: 'Analytics',
  trends: 'Trends Over Time',
  'category-comparison': 'Category Comparison',
  charts: 'Charts & Insights',
  categories: 'Categories',
  recurring: 'Recurring Transactions',
  settings: 'Settings',
  general: 'General Settings',
  preferences: 'Currency & Theme',
  integrations: 'Integrations',
  'import-export': 'Import/Export',
  profile: 'Profile',
  notifications: 'Notifications',
};

// Define parent-child relationships for better navigation
const routeParents: Record<string, string> = {
  add: 'transactions',
  edit: 'transactions',
  bank: 'accounts',
  cash: 'accounts',
  credit: 'accounts',
  create: 'budgets',
  tracking: 'budgets',
  savings: 'goals',
  debt: 'goals',
  expenses: 'reports',
  'income-expenses': 'reports',
  monthly: 'reports',
  yearly: 'reports',
  trends: 'analytics',
  'category-comparison': 'analytics',
  charts: 'analytics',
  general: 'settings',
  preferences: 'settings',
  integrations: 'settings',
  'import-export': 'settings',
};

export const useBreadcrumbs = (): BreadcrumbItem[] => {
  const location = useLocation();

  return useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Home/Dashboard as root
    breadcrumbs.push({
      label: 'Finance Tracker',
      href: '/dashboard',
    });

    // Build breadcrumbs from path segments
    let currentPath = '';
    
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      // Get the label for this segment
      const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      
      // Check if this segment has a parent that should be included
      const parent = routeParents[segment];
      if (parent && !breadcrumbs.some(b => b.href?.includes(parent))) {
        const parentLabel = routeLabels[parent] || parent.charAt(0).toUpperCase() + parent.slice(1);
        breadcrumbs.push({
          label: parentLabel,
          href: `/${parent}`,
        });
      }
      
      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        isCurrentPage: isLast,
      });
    });

    // Remove duplicates and ensure proper structure
    const uniqueBreadcrumbs = breadcrumbs.filter((item, index, arr) => 
      arr.findIndex(b => b.label === item.label) === index
    );

    return uniqueBreadcrumbs;
  }, [location.pathname]);
};
