# Dynamic Breadcrumbs Guide

## Overview

Your finance tracker now has fully dynamic breadcrumbs that automatically track the current page and show the navigation hierarchy. The breadcrumbs update automatically based on the current route.

## How It Works

### 1. **useBreadcrumbs Hook**
- Automatically generates breadcrumbs based on the current URL
- Maps route segments to human-readable labels
- Handles parent-child relationships between routes
- Returns an array of breadcrumb items

### 2. **DynamicBreadcrumb Component**
- Renders the breadcrumb navigation
- Uses React Router's Link for navigation
- Responsive design (hides first item on mobile)
- Shows current page as non-clickable text

### 3. **Route Mapping**
The system uses predefined mappings for better labels:

```typescript
const routeLabels = {
  dashboard: 'Dashboard',
  transactions: 'Transactions',
  add: 'Add Transaction',
  accounts: 'Accounts & Wallets',
  bank: 'Bank Accounts',
  // ... more mappings
}
```

## Examples

### Current Breadcrumb Behavior

| URL | Breadcrumb Display |
|-----|-------------------|
| `/dashboard` | Finance Tracker |
| `/transactions` | Finance Tracker > Transactions |
| `/transactions/add` | Finance Tracker > Transactions > Add Transaction |
| `/accounts/bank` | Finance Tracker > Accounts & Wallets > Bank Accounts |
| `/budgets/create` | Finance Tracker > Budgets > Create Budget |
| `/reports/expenses` | Finance Tracker > Reports > Expense Reports |
| `/analytics/trends` | Finance Tracker > Analytics > Trends Over Time |
| `/settings/general` | Finance Tracker > Settings > General Settings |

## Features

### ✅ **Automatic Route Detection**
- No manual configuration needed for each page
- Automatically detects current route and builds breadcrumbs

### ✅ **Parent-Child Relationships**
- Automatically includes parent routes when needed
- Example: `/accounts/bank` shows "Accounts & Wallets" before "Bank Accounts"

### ✅ **Responsive Design**
- First breadcrumb item hidden on mobile devices
- Separators also hidden on mobile for the first item

### ✅ **React Router Integration**
- Uses Link components for proper SPA navigation
- Current page shown as non-clickable text

### ✅ **Customizable Labels**
- Easy to add new route labels
- Human-readable names instead of URL segments

## Adding New Routes

### 1. **Add Route Label**
In `useBreadcrumbs.ts`, add to `routeLabels`:

```typescript
const routeLabels: Record<string, string> = {
  // ... existing labels
  'new-feature': 'New Feature Name',
  'sub-page': 'Sub Page Name',
}
```

### 2. **Add Parent Relationship (if needed)**
If your route has a parent, add to `routeParents`:

```typescript
const routeParents: Record<string, string> = {
  // ... existing relationships
  'sub-page': 'new-feature', // sub-page is under new-feature
}
```

### 3. **Add Route to Router**
Add the actual route in `routes/index.tsx`:

```typescript
{
  path: "new-feature",
  element: <DashboardLayout />,
  children: [
    {
      index: true,
      element: <>New Feature Page</>,
    },
    {
      path: "sub-page",
      element: <>Sub Page</>,
    },
  ],
}
```

## Customization Options

### **Change Root Label**
In `useBreadcrumbs.ts`, modify the root breadcrumb:

```typescript
breadcrumbs.push({
  label: 'Your App Name', // Change this
  href: '/dashboard',
});
```

### **Hide Breadcrumbs on Specific Pages**
In `DynamicBreadcrumb.tsx`, add conditions:

```typescript
const DynamicBreadcrumb: React.FC = () => {
  const breadcrumbs = useBreadcrumbs();
  const location = useLocation();
  
  // Hide on specific pages
  if (location.pathname === '/dashboard') {
    return null;
  }
  
  // ... rest of component
}
```

### **Custom Styling**
The breadcrumbs use shadcn UI components, so you can customize them by:
1. Modifying the Tailwind classes in `DynamicBreadcrumb.tsx`
2. Updating the shadcn breadcrumb component styles
3. Adding custom CSS classes

## File Structure

```
frontend/src/
├── hooks/
│   └── useBreadcrumbs.ts          # Breadcrumb logic
├── components/
│   └── DynamicBreadcrumb.tsx      # Breadcrumb component
├── layouts/
│   └── DashboardLayout.tsx        # Uses DynamicBreadcrumb
└── routes/
    └── index.tsx                  # Route definitions
```

## Benefits

1. **Automatic Updates** - No manual breadcrumb management needed
2. **Consistent Navigation** - Same breadcrumb behavior across all pages
3. **Better UX** - Users always know where they are in the app
4. **SEO Friendly** - Proper navigation structure for search engines
5. **Maintainable** - Easy to add new routes and update labels

The breadcrumb system is now fully functional and will automatically update as you navigate through your finance tracker application!
