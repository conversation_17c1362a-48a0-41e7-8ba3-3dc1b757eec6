import "reflect-metadata";
import { DataSource } from "typeorm";
// import { User } from "./entity/User";
// import { Transaction } from "./entity/Transaction";
// import { Category } from "./entity/Category";
// import { Budget } from "./entity/Budget";

export const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: Number(process.env.DB_PORT) || 3306,
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "finance_tracker",
  synchronize: true, // only for dev, use migrations in production
  logging: false,
  // entities: [User, Transaction, Category, Budget],
});
