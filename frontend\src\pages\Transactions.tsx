import React from 'react';

const Transactions: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Transactions</h1>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">
          This is the transactions page. Here you can view, add, edit, and delete your financial transactions.
        </p>
        <div className="mt-4 space-y-2">
          <h2 className="text-xl font-semibold">Features to implement:</h2>
          <ul className="list-disc list-inside space-y-1 text-gray-700">
            <li>Transaction list with filtering and sorting</li>
            <li>Add new transaction form</li>
            <li>Edit existing transactions</li>
            <li>Delete transactions</li>
            <li>Search functionality</li>
            <li>Category-based filtering</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Transactions;
