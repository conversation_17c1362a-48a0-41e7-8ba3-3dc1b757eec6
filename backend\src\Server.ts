import "reflect-metadata";
import dotenv from "dotenv";
import app from "./App";
import { AppDataSource } from "./data-source";

dotenv.config();

const PORT = process.env.PORT || 5000;

AppDataSource.initialize()
  .then(() => {
    console.log("✅ Database connected");
    app.listen(PORT, () => {
      console.log(`🚀 Server running at http://localhost:${PORT}`);
    });
  })
  .catch((error) => console.error("❌ DB connection failed", error));
