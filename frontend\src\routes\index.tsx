import { create<PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import RootLayout from "../layouts/RootLayout";
import DashboardLayout from "../layouts/DashboardLayout";
import AuthLayout from "@/layouts/AuthLayout";
import Dashboard from "@/pages/Dashboard";
import Transactions from "@/pages/Transactions";
import NotFoundPage from "@/pages/NotFound";

const routes = [
  {
    path: "/",
    element: <RootLayout />,
    errorElement: <div>Loading...</div>,
    children: [
      {
        index: true,
        element: <>Home Page</>,
      },
      {
        path: "auth",
        element: <AuthLayout />,
        children: [
          {
            path: "login",
            element: <>Login Page</>,
          },
          {
            path: "register",
            element: <>Register Page</>,
          },
        ],
      },
      {
        path: "dashboard",
        element: <DashboardLayout />, //later we add it in protected routes
        children: [
          {
            index: true,
            element: <Dashboard />,
          },
        ],
      },
      {
        path: "transactions",
        element: <DashboardLayout />,
        children: [
          {
            index: true,
            element: <Transactions />,
          },
          {
            path: "add",
            element: <>Add Transaction Page</>,
          },
        ],
      },
      {
        path: "accounts",
        element: <DashboardLayout />,
        children: [
          {
            path: "bank",
            element: <>Bank Accounts Page</>,
          },
          {
            path: "cash",
            element: <>Cash & Wallets Page</>,
          },
          {
            path: "credit",
            element: <>Credit Cards Page</>,
          },
        ],
      },
      {
        path: "budgets",
        element: <DashboardLayout />,
        children: [
          {
            index: true,
            element: <>Budgets Page</>,
          },
          {
            path: "create",
            element: <>Create Budget Page</>,
          },
          {
            path: "tracking",
            element: <>Budget Tracking Page</>,
          },
        ],
      },
      {
        path: "profile",
        element: <DashboardLayout />,
        children: [
          {
            index: true,
            element: <>Profile Page</>,
          },
        ],
      },
      {
        path: "settings",
        element: <DashboardLayout />,
        children: [
          {
            index: true,
            element: <>Settings Page</>,
          },
        ],
      },
      {
        path: "*",
        element: <NotFoundPage />, //later add a not found component here
      },
    ],
  },
];

const router = createBrowserRouter(routes);
export default router;
