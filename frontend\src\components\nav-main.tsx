import { ChevronRight, type LucideIcon } from "lucide-react"
import { Link } from "react-router-dom"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  // Group items by section
  const coreItems = items.slice(0, 3) // Dashboard, Transactions, Accounts
  const budgetingItems = items.slice(3, 5) // Budgets, Goals
  const analyticsItems = items.slice(5, 7) // Reports, Analytics
  const settingsItems = items.slice(7) // Categories, Recurring, Settings

  const renderMenuItems = (sectionItems: typeof items) => (
    sectionItems.map((item) => (
      item.items ? (
        <Collapsible
          key={item.title}
          asChild
          defaultOpen={item.isActive}
          className="group/collapsible"
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip={item.title}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items?.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton asChild>
                      <Link to={subItem.url}>
                        <span>{subItem.title}</span>
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      ) : (
        <SidebarMenuItem key={item.title}>
          <SidebarMenuButton asChild tooltip={item.title}>
            <Link to={item.url}>
              {item.icon && <item.icon />}
              <span>{item.title}</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )
    ))
  )

  return (
    <>
      {/* Core Sections */}
      <SidebarGroup>
        <SidebarGroupLabel>📊 Core</SidebarGroupLabel>
        <SidebarMenu>
          {renderMenuItems(coreItems)}
        </SidebarMenu>
      </SidebarGroup>

      {/* Budgeting & Planning */}
      <SidebarGroup>
        <SidebarGroupLabel>💰 Budgeting & Planning</SidebarGroupLabel>
        <SidebarMenu>
          {renderMenuItems(budgetingItems)}
        </SidebarMenu>
      </SidebarGroup>

      {/* Analytics & Reports */}
      <SidebarGroup>
        <SidebarGroupLabel>📈 Analytics & Reports</SidebarGroupLabel>
        <SidebarMenu>
          {renderMenuItems(analyticsItems)}
        </SidebarMenu>
      </SidebarGroup>

      {/* Settings & Config */}
      <SidebarGroup>
        <SidebarGroupLabel>🛠️ Settings & Config</SidebarGroupLabel>
        <SidebarMenu>
          {renderMenuItems(settingsItems)}
        </SidebarMenu>
      </SidebarGroup>
    </>
  )
}
