# Finance Tracker Sidebar Guide

## Overview

Your shadcn UI sidebar has been completely updated to match your finance tracker requirements. The sidebar is now organized into logical sections with proper icons and navigation structure.

## Sidebar Structure

### 📊 Core Sections
- **Dashboard** - Quick snapshot (balances, spending, income, charts)
- **Transactions** - View all, add/edit/delete transactions
- **Accounts & Wallets** - Bank accounts, cash/wallets, credit cards

### 💰 Budgeting & Planning
- **Budgets** - Create/edit budgets, track spending vs budget
- **Goals** - Savings goals, debt repayment goals

### 📈 Analytics & Reports
- **Reports** - Expense by category, income vs expenses, summaries
- **Analytics** - Trends over time, category comparisons, charts

### 🛠️ Settings & Config
- **Categories** - Manage expense/income categories
- **Recurring Transactions** - Subscriptions, bills, salaries
- **Settings** - Currency, theme, integrations, import/export

### 👤 User Section (Footer)
- **Profile** - User details, password, security
- **Notifications** - Reminders, bill alerts
- **Logout**

## Key Features

1. **Collapsible Sidebar** - Icon mode when collapsed
2. **Organized Sections** - Grouped by functionality with emoji labels
3. **React Router Integration** - Uses Link components for navigation
4. **Responsive Design** - Works on mobile and desktop
5. **Icon Support** - Lucide React icons for all menu items

## How to Add New Menu Items

### Adding a Simple Menu Item

```tsx
// In app-sidebar.tsx, add to the navMain array:
{
  title: "New Feature",
  url: "/new-feature",
  icon: YourIcon, // Import from lucide-react
}
```

### Adding a Menu Item with Submenu

```tsx
{
  title: "Parent Menu",
  url: "#",
  icon: YourIcon,
  items: [
    {
      title: "Submenu 1",
      url: "/parent/submenu1",
    },
    {
      title: "Submenu 2", 
      url: "/parent/submenu2",
    },
  ],
}
```

## Customization Options

### Changing Section Labels
Edit the `SidebarGroupLabel` components in `nav-main.tsx`:

```tsx
<SidebarGroupLabel>🎯 Your Custom Section</SidebarGroupLabel>
```

### Updating App Title
In `app-sidebar.tsx`, modify the header:

```tsx
<div className="flex items-center gap-2 px-4 py-2">
  <YourIcon className="h-6 w-6 text-green-600" />
  <span className="font-semibold text-lg">Your App Name</span>
</div>
```

### Adding New Routes
1. Create your page component in `src/pages/`
2. Import it in `src/routes/index.tsx`
3. Add the route configuration
4. Update the sidebar navigation URLs

## Current Route Structure

The following routes are already configured:
- `/dashboard` - Main dashboard
- `/transactions` - Transaction management
- `/transactions/add` - Add new transaction
- `/accounts/bank` - Bank accounts
- `/accounts/cash` - Cash & wallets
- `/accounts/credit` - Credit cards
- `/budgets` - Budget management
- `/budgets/create` - Create budget
- `/budgets/tracking` - Budget tracking

## Next Steps

1. **Create Page Components** - Build the actual pages for each route
2. **Add Authentication** - Protect routes with auth guards
3. **Implement State Management** - Add Redux/Zustand for app state
4. **Add Real Data** - Connect to your backend API
5. **Enhance UI** - Add loading states, error handling, etc.

## File Structure

```
frontend/src/
├── components/
│   ├── app-sidebar.tsx      # Main sidebar component
│   ├── nav-main.tsx         # Navigation menu logic
│   ├── nav-user.tsx         # User dropdown menu
│   └── ui/                  # shadcn UI components
├── layouts/
│   └── DashboardLayout.tsx  # Layout with sidebar
├── pages/                   # Your page components
├── routes/
│   └── index.tsx           # Route configuration
```

The sidebar is now ready for your finance tracker application!
