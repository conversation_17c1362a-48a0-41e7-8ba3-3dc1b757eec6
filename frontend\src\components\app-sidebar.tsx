"use client"

import * as React from "react"
import {
  FileText,
  Goal,
  Home,
  IndianRupee,
  PieChart, Receipt,
  Repeat,
  Settings,
  Target,
  TrendingUp, Wallet
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// Finance Tracker Navigation Data
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  navMain: [
    // 📊 Core Sections
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Home,
      isActive: true,
    },
    {
      title: "Transactions",
      url: "#",
      icon: Receipt,
      items: [
        {
          title: "View All Transactions",
          url: "/transactions",
        },
        {
          title: "Add Transaction",
          url: "/transactions/add",
        },
      ],
    },
    {
      title: "Accounts & Wallets",
      url: "#",
      icon: Wallet,
      items: [
        {
          title: "Bank Accounts",
          url: "/accounts/bank",
        },
        {
          title: "Cash & Wallets",
          url: "/accounts/cash",
        },
        {
          title: "Credit Cards",
          url: "/accounts/credit",
        },
      ],
    },
    // 💰 Budgeting & Planning
    {
      title: "Budgets",
      url: "#",
      icon: Target,
      items: [
        {
          title: "View Budgets",
          url: "/budgets",
        },
        {
          title: "Create Budget",
          url: "/budgets/create",
        },
        {
          title: "Budget vs Spending",
          url: "/budgets/tracking",
        },
      ],
    },
    {
      title: "Goals",
      url: "#",
      icon: Goal,
      items: [
        {
          title: "Savings Goals",
          url: "/goals/savings",
        },
        {
          title: "Debt Repayment",
          url: "/goals/debt",
        },
      ],
    },
    // 📈 Analytics & Reports
    {
      title: "Reports",
      url: "#",
      icon: FileText,
      items: [
        {
          title: "Expense by Category",
          url: "/reports/expenses",
        },
        {
          title: "Income vs Expenses",
          url: "/reports/income-expenses",
        },
        {
          title: "Monthly Summary",
          url: "/reports/monthly",
        },
        {
          title: "Yearly Summary",
          url: "/reports/yearly",
        },
      ],
    },
    {
      title: "Analytics",
      url: "#",
      icon: TrendingUp,
      items: [
        {
          title: "Trends Over Time",
          url: "/analytics/trends",
        },
        {
          title: "Category Comparison",
          url: "/analytics/categories",
        },
        {
          title: "Charts & Insights",
          url: "/analytics/charts",
        },
      ],
    },
    // 🛠️ Settings & Config
    {
      title: "Categories",
      url: "/categories",
      icon: PieChart,
    },
    {
      title: "Recurring Transactions",
      url: "/recurring",
      icon: Repeat,
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings,
      items: [
        {
          title: "General Settings",
          url: "/settings/general",
        },
        {
          title: "Currency & Theme",
          url: "/settings/preferences",
        },
        {
          title: "Integrations",
          url: "/settings/integrations",
        },
        {
          title: "Import/Export",
          url: "/settings/import-export",
        },
      ],
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2">
          <IndianRupee className="h-6 w-6 text-green-600 flex-shrink-0" />
          <span className="font-semibold text-lg group-data-[collapsible=icon]:hidden">
            Finance Tracker
          </span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
