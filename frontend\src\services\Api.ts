import axios from "axios";

// Create an Axios instance
export const api = axios.create({
  baseURL: "/api", // points to backend (CRA or Vite proxy)
  timeout: 5000, // optional: timeout in ms
});

// Optional: add interceptors for auth headers
api.interceptors.request.use((config) => {
  const token = localStorage.getItem("token"); // if using JWT
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
